---
import astroLogo from '../assets/astro.svg';
import background from '../assets/background.svg';
---

<div class="min-h-screen relative overflow-hidden">
	<img class="fixed inset-0 w-full h-full -z-10 blur-[100px]" src={background.src} alt="" fetchpriority="high" />
	<main class="h-screen flex justify-center items-center">
		<section class="flex flex-col items-start justify-center p-4 max-w-2xl">
			<a href="https://astro.build" class="mb-4">
				<img src={astroLogo.src} width="115" height="48" alt="Astro Homepage" />
			</a>
			<h1 class="text-2xl font-semibold mb-6 text-gray-900">
				Welcome to <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-bold">Polar Image Store</span>
				powered by Astro and Tailwind CSS v4!
			</h1>
			<div class="flex gap-4 flex-wrap">
				<a
					class="flex items-center px-6 py-3 text-white bg-gradient-to-r from-primary-600 to-purple-600 rounded-full font-medium transition-all hover:shadow-lg hover:-translate-y-0.5"
					href="/products"
				>
					Browse Products
				</a>
				<a
					href="https://docs.astro.build"
					class="flex items-center px-6 py-3 text-gray-700 border border-gray-300 rounded-full font-medium transition-colors hover:bg-gray-50"
				>
					Astro Docs
					<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
					</svg>
				</a>
			</div>
		</section>
	</main>

	<a
		href="https://tailwindcss.com/blog/tailwindcss-v4-0"
		class="fixed bottom-4 right-4 max-w-sm p-4 bg-white/90 backdrop-blur-md rounded-2xl border border-white shadow-lg transition-all hover:bg-white/95 hover:shadow-xl text-decoration-none"
	>
		<div class="flex items-start gap-3">
			<div class="w-8 h-8 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
				<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
					<path d="M12.001 4.8c-3.2 0-5.2 1.6-6 4.8 1.2-1.6 2.6-2.2 4.2-1.8.913.228 1.565.89 2.288 1.624C13.666 10.618 15.027 12 18.001 12c3.2 0 5.2-1.6 6-4.8-1.2 1.6-2.6 2.2-4.2 1.8-.913-.228-1.565-.89-2.288-1.624C16.337 6.182 14.976 4.8 12.001 4.8zm-6 7.2c-3.2 0-5.2 1.6-6 4.8 1.2-1.6 2.6-2.2 4.2-1.8.913.228 1.565.89 2.288 1.624C7.666 17.818 9.027 19.2 12.001 19.2c3.2 0 5.2-1.6 6-4.8-1.2 1.6-2.6 2.2-4.2 1.8-.913-.228-1.565-.89-2.288-1.624C10.337 13.382 8.976 12 6.001 12z"/>
				</svg>
			</div>
			<div>
				<h3 class="font-semibold text-gray-900 mb-1">Tailwind CSS v4.0</h3>
				<p class="text-sm text-gray-600 leading-relaxed">
					Now with CSS-first configuration, zero dependencies, and modern CSS features!
				</p>
			</div>
		</div>
	</a>
</div>
