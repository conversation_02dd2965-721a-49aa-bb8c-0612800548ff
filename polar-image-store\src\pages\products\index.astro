---
import Layout from '../../layouts/Layout.astro';
import ProductCard from '../../components/ProductCard.astro';
import SearchFilter from '../../components/SearchFilter.astro';
import { createPolarClient, transformPolarProduct } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

let products: LocalProduct[] = [];
let error: string | null = null;

try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
  
  if (organizationId) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    products = productList.map(transformPolarProduct);
  } else {
    error = 'Organization ID not configured';
  }
} catch (e) {
  console.error('Error fetching products:', e);
  error = 'Failed to load products';
}
---

<Layout title="Products - Polar Image Store" description="Browse our collection of beautiful digital images">
  <section class="text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">Our Collection</h1>
    <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover beautiful digital images for your projects</p>
  </section>

  <SearchFilter />

  {error && (
    <div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
      <div class="flex items-center gap-3 text-red-800">
        <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <p class="font-semibold">⚠️ {error}</p>
          <p class="text-sm">Please check your configuration and try again.</p>
        </div>
      </div>
    </div>
  )}

  {!error && products.length === 0 && (
    <div class="text-center py-16">
      <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
      </div>
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products available</h2>
      <p class="text-gray-600">Check back soon for new additions to our collection!</p>
    </div>
  )}
  
  {!error && products.length > 0 && (
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="productsGrid">
      {products.map((product) => (
        <ProductCard product={product} />
      ))}
    </div>
  )}

  <!-- Loading state for filtered results -->
  <div class="hidden text-center py-16" id="loadingState">
    <div class="inline-flex items-center gap-3 text-gray-600">
      <svg class="animate-spin w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
      <span>Filtering products...</span>
    </div>
  </div>

  <!-- No results state -->
  <div class="hidden text-center py-16" id="noResults">
    <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
      <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
    </div>
    <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products found</h2>
    <p class="text-gray-600">Try adjusting your search or filters</p>
  </div>
</Layout>



<script>
  // Handle filtered products from SearchFilter component
  document.addEventListener('productsFiltered', function(event) {
    const filteredProducts = event.detail.products;
    renderFilteredProducts(filteredProducts);
  });

  function renderFilteredProducts(products) {
    const grid = document.getElementById('productsGrid');
    const loading = document.getElementById('loadingState');
    const noResults = document.getElementById('noResults');

    if (!grid) return;

    // Show loading state briefly
    grid.classList.add('opacity-60', 'pointer-events-none');
    loading.classList.remove('hidden');
    noResults.classList.add('hidden');

    setTimeout(() => {
      if (products.length === 0) {
        grid.classList.add('hidden');
        loading.classList.add('hidden');
        noResults.classList.remove('hidden');
      } else {
        // Generate HTML for filtered products using Tailwind classes
        grid.innerHTML = products.map(product => `
          <div class="group bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
            ${product.images.length > 0 ? `
              <div class="relative h-48 overflow-hidden bg-gray-50">
                <img src="${product.images[0]}" alt="${product.name}" loading="lazy" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
                <div class="absolute inset-0 bg-black/70 flex items-center justify-center gap-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <a href="/products/${product.slug}" class="px-5 py-2 bg-transparent border-2 border-white text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:text-gray-900">View Details</a>
                  <a href="/api/checkout?product_id=${product.id}" class="px-5 py-2 bg-primary-600 border-2 border-primary-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-primary-700 hover:border-primary-700 hover:-translate-y-0.5">Buy Now</a>
                </div>
              </div>
            ` : ''}
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">${product.name}</h3>
              <p class="text-gray-600 text-sm mb-4 line-clamp-3">${product.description}</p>
              ${product.tags && product.tags.length > 0 ? `
                <div class="flex flex-wrap gap-2 mb-4">
                  ${product.tags.slice(0, 3).map(tag => `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">#${tag}</span>`).join('')}
                </div>
              ` : ''}
              <div class="flex items-center justify-between">
                <span class="text-2xl font-bold text-gray-900">$${product.price.toFixed(2)}</span>
                <div class="flex gap-2">
                  <a href="/products/${product.slug}" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-full text-sm font-medium transition-colors hover:bg-gray-50 hover:text-gray-900">Details</a>
                  <a href="/api/checkout?product_id=${product.id}" class="px-4 py-2 bg-primary-600 text-white rounded-full text-sm font-medium transition-colors hover:bg-primary-700">Buy</a>
                </div>
              </div>
            </div>
          </div>
        `).join('');

        grid.classList.remove('hidden');
        loading.classList.add('hidden');
        noResults.classList.add('hidden');
      }

      grid.classList.remove('opacity-60', 'pointer-events-none');
    }, 300);
  }
</script>
