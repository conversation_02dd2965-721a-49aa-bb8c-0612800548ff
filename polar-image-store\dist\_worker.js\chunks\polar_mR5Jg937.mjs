globalThis.process ??= {}; globalThis.process.env ??= {};
import { P as Polar } from './sdk_DEQ9AU5A.mjs';

function createPolarClient() {
  const accessToken = "polar_oat_ouH54pn5flleg2O77vuEz0JdL0plgG6L9u74d4QLM5Z";
  return new Polar({
    accessToken,
    server: "production"
    // Always use production
  });
}
function transformPolarProduct(polarProduct) {
  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || "USD";
  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || "",
    price: price / 100,
    // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: extractTags(polarProduct.description || ""),
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}
function generateSlug(name) {
  return name.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-+|-+$/g, "");
}
function extractTags(description) {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map((tag) => tag.slice(1)) : [];
}
function formatPrice(price, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase()
  }).format(price);
}

export { createPolarClient as c, formatPrice as f, transformPolarProduct as t };
