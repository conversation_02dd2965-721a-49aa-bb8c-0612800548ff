{"name": "polar-image-store", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "deploy": "astro build && wrangler pages deploy dist", "deploy:staging": "astro build && wrangler pages deploy dist --env staging", "deploy:production": "astro build && wrangler pages deploy dist --env production", "test:polar": "node scripts/test-polar.js"}, "dependencies": {"@polar-sh/astro": "^0.4.4", "@polar-sh/sdk": "^0.34.8", "@tailwindcss/vite": "^4.1.11", "astro": "^5.12.4", "tailwindcss": "^4.1.11", "zod": "^4.0.11"}, "devDependencies": {"@types/node": "^24.1.0", "dotenv": "^17.2.1", "wrangler": "^4.26.0"}}