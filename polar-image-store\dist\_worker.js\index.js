globalThis.process ??= {}; globalThis.process.env ??= {};
import { renderers } from './renderers.mjs';
import { c as createExports } from './chunks/server_DGdhC5cH.mjs';
import { manifest } from './manifest_D2BruYqQ.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/api/checkout.astro.mjs');
const _page2 = () => import('./pages/api/products.astro.mjs');
const _page3 = () => import('./pages/api/webhooks.astro.mjs');
const _page4 = () => import('./pages/products/_slug_.astro.mjs');
const _page5 = () => import('./pages/products.astro.mjs');
const _page6 = () => import('./pages/success.astro.mjs');
const _page7 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint.js", _page0],
    ["src/pages/api/checkout.ts", _page1],
    ["src/pages/api/products.ts", _page2],
    ["src/pages/api/webhooks.ts", _page3],
    ["src/pages/products/[slug].astro", _page4],
    ["src/pages/products/index.astro", _page5],
    ["src/pages/success.astro", _page6],
    ["src/pages/index.astro", _page7]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_astro-internal_middleware.mjs')
});
const _exports = createExports(_manifest);
const __astrojsSsrVirtualEntry = _exports.default;

export { __astrojsSsrVirtualEntry as default, pageMap };
