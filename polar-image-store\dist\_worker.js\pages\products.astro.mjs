globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createComponent, a as createAstro, m as maybeRenderHead, b as addAttribute, f as renderScript, r as renderTemplate, g as renderComponent } from '../chunks/astro/server_DkHHQTPZ.mjs';
import { $ as $$Layout } from '../chunks/Layout_R4AeOjRm.mjs';
import { $ as $$ProductCard } from '../chunks/ProductCard_Ca_HeVQD.mjs';
/* empty css                                 */
import { c as createPolarClient, t as transformPolarProduct } from '../chunks/polar_mR5Jg937.mjs';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro();
const $$SearchFilter = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$SearchFilter;
  const {
    initialSearch = "",
    initialPriceRange = [0, 100],
    initialTags = []
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div class="bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-100" data-astro-cid-qs3y32ah> <div class="mb-6" data-astro-cid-qs3y32ah> <div class="flex gap-3" data-astro-cid-qs3y32ah> <input type="text" id="searchInput" placeholder="Search products..."${addAttribute(initialSearch, "value")} class="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors" data-astro-cid-qs3y32ah> <button class="px-6 py-3 bg-primary-600 text-white rounded-full hover:bg-primary-700 transition-colors font-medium" onclick="performSearch()" data-astro-cid-qs3y32ah>
🔍
</button> </div> </div> <div class="flex items-center justify-between" data-astro-cid-qs3y32ah> <button id="filterToggleBtn" class="flex items-center gap-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors" data-astro-cid-qs3y32ah> <span data-astro-cid-qs3y32ah>Filters</span> <span data-astro-cid-qs3y32ah>⚙️</span> </button> <div class="hidden mt-6 p-6 bg-gray-50 rounded-full border border-gray-200" id="filterPanel" data-astro-cid-qs3y32ah> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-astro-cid-qs3y32ah> <div class="space-y-3" data-astro-cid-qs3y32ah> <label class="block text-sm font-medium text-gray-700" data-astro-cid-qs3y32ah>Price Range</label> <div class="space-y-3" data-astro-cid-qs3y32ah> <input type="range" id="minPrice" min="0" max="100"${addAttribute(initialPriceRange[0], "value")} class="w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer slider" oninput="updatePriceRange()" data-astro-cid-qs3y32ah> <input type="range" id="maxPrice" min="0" max="100"${addAttribute(initialPriceRange[1], "value")} class="w-full h-2 bg-gray-200 rounded-xl appearance-none cursor-pointer slider" oninput="updatePriceRange()" data-astro-cid-qs3y32ah> <div class="text-center" data-astro-cid-qs3y32ah> <span id="priceDisplay" class="text-sm font-medium text-gray-600" data-astro-cid-qs3y32ah>$0 - $100</span> </div> </div> </div> <div class="space-y-3" data-astro-cid-qs3y32ah> <label class="block text-sm font-medium text-gray-700" data-astro-cid-qs3y32ah>Sort By</label> <select id="sortBy" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors bg-white" onchange="applySorting()" data-astro-cid-qs3y32ah> <option value="name" data-astro-cid-qs3y32ah>Name (A-Z)</option> <option value="name-desc" data-astro-cid-qs3y32ah>Name (Z-A)</option> <option value="price" data-astro-cid-qs3y32ah>Price (Low to High)</option> <option value="price-desc" data-astro-cid-qs3y32ah>Price (High to Low)</option> <option value="newest" data-astro-cid-qs3y32ah>Newest First</option> <option value="oldest" data-astro-cid-qs3y32ah>Oldest First</option> </select> </div> <div class="space-y-3" data-astro-cid-qs3y32ah> <label class="block text-sm font-medium text-gray-700" data-astro-cid-qs3y32ah>Tags</label> <div class="flex flex-wrap gap-2" id="tagsFilter" data-astro-cid-qs3y32ah> <!-- Tags will be populated dynamically --> </div> </div> <div class="space-y-3" data-astro-cid-qs3y32ah> <label class="block text-sm font-medium text-gray-700" data-astro-cid-qs3y32ah>Actions</label> <div class="flex gap-2" data-astro-cid-qs3y32ah> <button class="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors text-sm font-medium" onclick="clearAllFilters()" data-astro-cid-qs3y32ah>
Clear All
</button> <button class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 transition-colors text-sm font-medium" onclick="applyFilters()" data-astro-cid-qs3y32ah>
Apply
</button> </div> </div> </div> </div> </div> <div class="mt-4" data-astro-cid-qs3y32ah> <span id="resultsCount" class="text-sm text-gray-600" data-astro-cid-qs3y32ah>Loading products...</span> </div> </div>  ${renderScript($$result, "D:/code/image/polar-image-store/src/components/SearchFilter.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/components/SearchFilter.astro", void 0);

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  let products = [];
  let error = null;
  try {
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (organizationId) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false
      });
      const productList = response.result?.items || [];
      products = productList.map(transformPolarProduct);
    }
  } catch (e) {
    console.error("Error fetching products:", e);
    error = "Failed to load products";
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Products - Polar Image Store", "description": "Browse our collection of beautiful digital images" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">Our Collection</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover beautiful digital images for your projects</p> </section> ${renderComponent($$result2, "SearchFilter", $$SearchFilter, {})} ${error && renderTemplate`<div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8"> <div class="flex items-center gap-3 text-red-800"> <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path> </svg> <div> <p class="font-semibold">⚠️ ${error}</p> <p class="text-sm">Please check your configuration and try again.</p> </div> </div> </div>`}${!error && products.length === 0 && renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg> </div> <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products available</h2> <p class="text-gray-600">Check back soon for new additions to our collection!</p> </div>`}${!error && products.length > 0 && renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="productsGrid"> ${products.map((product) => renderTemplate`${renderComponent($$result2, "ProductCard", $$ProductCard, { "product": product })}`)} </div>`} <div class="hidden text-center py-16" id="loadingState"> <div class="inline-flex items-center gap-3 text-gray-600"> <svg class="animate-spin w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path> </svg> <span>Filtering products...</span> </div> </div>  <div class="hidden text-center py-16" id="noResults"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> </div> <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products found</h2> <p class="text-gray-600">Try adjusting your search or filters</p> </div> ` })} ${renderScript($$result, "D:/code/image/polar-image-store/src/pages/products/index.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/pages/products/index.astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/products/index.astro";
const $$url = "/products";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
