---
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Beautiful digital images from our collection" } = Astro.props;
const siteUrl = import.meta.env.PUBLIC_SITE_URL;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={siteUrl} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={siteUrl} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />

    <title>{title}</title>
  </head>
  <body class="min-h-screen flex flex-col">
    <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4">
      <div class="container">
        <nav class="flex items-center justify-between">
          <!-- Logo -->
          <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors">
            <svg class="w-8 h-8 text-accent-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
            Polar Image Store
          </a>

          <!-- Search Bar -->
          <div class="hidden md:flex flex-1 max-w-md mx-8">
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                id="productSearch"
                class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
                placeholder="Search products..."
                autocomplete="off"
              />
              <!-- Search results dropdown (hidden by default) -->
              <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto">
                <!-- Search results will be populated here -->
              </div>
            </div>
          </div>

          <!-- CTA Button & Mobile Menu -->
          <div class="flex items-center gap-4">
            <a href="/products" class="btn-primary hidden md:inline-flex">
              Browse Collection
            </a>

            <!-- Mobile menu button -->
            <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </nav>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
          <div class="pt-4 pb-2 border-t border-primary-100 mt-4">
            <!-- Mobile Search -->
            <div class="mb-4">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  id="mobileProductSearch"
                  class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200"
                  placeholder="Search products..."
                  autocomplete="off"
                />
              </div>
            </div>

            <!-- Mobile Navigation -->
            <ul class="space-y-2">
              <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li>
              <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li>
              <li><a href="/gallery" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Gallery</a></li>
              <li><a href="/pricing" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Pricing</a></li>
              <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li>
            </ul>
            <div class="mt-4 pt-4 border-t border-primary-100">
              <a href="/products" class="btn-primary w-full justify-center">
                Browse Collection
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <main class="flex-1">
      <slot />
    </main>

    <footer class="bg-primary-50 border-t border-primary-100 py-12 text-center text-primary-600">
      <div class="container">
        <div class="flex flex-col items-center gap-4">
          <div class="flex items-center gap-2 text-lg font-semibold text-primary-900">
            <svg class="w-6 h-6 text-accent-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
            Polar Image Store
          </div>
          <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p>
          <p class="text-sm">Powered by <a href="https://polar.sh" target="_blank" rel="noopener" class="text-accent-600 hover:text-accent-700 font-medium transition-colors">Polar.sh</a></p>
        </div>
      </div>
    </footer>
  </body>
</html>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', () => {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // Search functionality
    const searchInput = document.getElementById('productSearch');
    const mobileSearchInput = document.getElementById('mobileProductSearch');
    const searchResults = document.getElementById('searchResults');

    // Basic search functionality (can be enhanced later)
    function handleSearch(input) {
      const query = input.value.trim();

      if (query.length > 2) {
        // Show search results dropdown
        if (searchResults) {
          searchResults.classList.remove('hidden');
          searchResults.innerHTML = `
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching for "${query}"...</span>
              </div>
            </div>
          `;

          // Simulate search delay
          setTimeout(() => {
            if (searchResults && !searchResults.classList.contains('hidden')) {
              searchResults.innerHTML = `
                <div class="p-4">
                  <div class="text-sm text-primary-600 mb-2">Search results for "${query}"</div>
                  <a href="/products" class="block p-3 hover:bg-primary-50 rounded-lg transition-colors">
                    <div class="text-primary-900 font-medium">View all products</div>
                    <div class="text-primary-600 text-sm">Browse our complete collection</div>
                  </a>
                </div>
              `;
            }
          }, 500);
        }
      } else {
        // Hide search results
        if (searchResults) {
          searchResults.classList.add('hidden');
        }
      }
    }

    // Add search event listeners
    if (searchInput) {
      searchInput.addEventListener('input', (e) => handleSearch(e.target));
      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            window.location.href = `/products?search=${encodeURIComponent(query)}`;
          }
        }
      });
    }

    if (mobileSearchInput) {
      mobileSearchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = e.target.value.trim();
          if (query) {
            window.location.href = `/products?search=${encodeURIComponent(query)}`;
          }
        }
      });
    }

    // Hide search results when clicking outside
    document.addEventListener('click', (e) => {
      if (searchResults && !searchInput?.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.classList.add('hidden');
      }
    });
  });
</script>
