globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createComponent, a as createAstro, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate, f as renderScript, b as addAttribute, g as renderComponent } from '../chunks/astro/server_DkHHQTPZ.mjs';
import { $ as $$Layout } from '../chunks/Layout_R4AeOjRm.mjs';
/* empty css                                 */
import { $ as $$ProductCard } from '../chunks/ProductCard_Ca_HeVQD.mjs';
import { c as createPolarClient, t as transformPolarProduct } from '../chunks/polar_mR5Jg937.mjs';
export { renderers } from '../renderers.mjs';

const $$Astro$1 = createAstro();
const $$Hero = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$Hero;
  const {
    title,
    subtitle
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<section class="relative py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden" data-astro-cid-bbe6dxrz> <!-- Background decoration --> <div class="absolute inset-0 bg-grid-pattern opacity-5" data-astro-cid-bbe6dxrz></div> <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="container relative" data-astro-cid-bbe6dxrz> <div class="max-w-4xl mx-auto" data-astro-cid-bbe6dxrz> <!-- Main title --> <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-4 leading-tight max-w-3xl text-left" data-astro-cid-bbe6dxrz>${unescapeHTML(title)}</h1> <!-- Subtitle --> <p class="text-base md:text-lg lg:text-xl text-primary-600 max-w-2xl leading-relaxed" data-astro-cid-bbe6dxrz> ${subtitle} </p> </div> </div> </section> `;
}, "D:/code/image/polar-image-store/src/components/Hero.astro", void 0);

const $$Astro = createAstro();
const $$CategoryNavigation = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$CategoryNavigation;
  const defaultCategories = [
    { id: "all", name: "All", count: 24 },
    { id: "photography", name: "Photography", count: 8 },
    { id: "digital-art", name: "Digital Art", count: 6 },
    { id: "illustrations", name: "Illustrations", count: 5 },
    { id: "abstract", name: "Abstract", count: 3 },
    { id: "nature", name: "Nature", count: 4 },
    { id: "portraits", name: "Portraits", count: 2 },
    { id: "landscapes", name: "Landscapes", count: 3 }
  ];
  const {
    categories = defaultCategories,
    activeCategory = "all"
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<section class="py-8 bg-white border-b border-primary-100" data-astro-cid-hvggfmtz> <div class="container" data-astro-cid-hvggfmtz> <div class="flex items-center justify-between mb-6" data-astro-cid-hvggfmtz> <h2 class="text-2xl font-bold text-primary-900" data-astro-cid-hvggfmtz>Browse by Category</h2> <div class="text-sm text-primary-600" data-astro-cid-hvggfmtz> ${categories.reduce((total, cat) => total + (cat.count || 0), 0)} total items
</div> </div> <!-- Category Navigation --> <div class="relative" data-astro-cid-hvggfmtz> <!-- Scroll container --> <div class="overflow-x-auto scrollbar-hide" id="categoryScroll" data-astro-cid-hvggfmtz> <div class="flex gap-2 pb-2 min-w-max" data-astro-cid-hvggfmtz> ${categories.map((category) => renderTemplate`<button${addAttribute(`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${activeCategory === category.id ? "bg-accent-600 text-white shadow-md" : "bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900"}`, "class")}${addAttribute(category.id, "data-category")} data-astro-cid-hvggfmtz> ${category.name} ${category.count && renderTemplate`<span${addAttribute(`text-xs px-2 py-0.5 rounded-full ${activeCategory === category.id ? "bg-white/20 text-white" : "bg-primary-200 text-primary-600"}`, "class")} data-astro-cid-hvggfmtz> ${category.count} </span>`} </button>`)} </div> </div> <!-- Scroll buttons --> <button class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none" id="scrollLeft" data-astro-cid-hvggfmtz> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-hvggfmtz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-astro-cid-hvggfmtz></path> </svg> </button> <button class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none" id="scrollRight" data-astro-cid-hvggfmtz> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-hvggfmtz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-astro-cid-hvggfmtz></path> </svg> </button> </div> </div> </section>  ${renderScript($$result, "D:/code/image/polar-image-store/src/components/CategoryNavigation.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/components/CategoryNavigation.astro", void 0);

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  let featuredProducts = [];
  let error = null;
  try {
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (organizationId) {
      const response = await polar.products.list({
        organizationId,
        isArchived: false,
        limit: 6
        // Show only 6 featured products on homepage
      });
      const productList = response.result?.items || [];
      featuredProducts = productList.map(transformPolarProduct);
    }
  } catch (e) {
    console.error("Error fetching featured products:", e);
    error = "Failed to load featured products";
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Polar Image Store - Premium Digital Images & Artwork" }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "Hero", $$Hero, { "title": "Premium digital images crafted<br>with passion and creativity", "subtitle": "A carefully built collection of fast, clean templates made to help you launch faster — without bloated code or generic design" })}  ${renderComponent($$result2, "CategoryNavigation", $$CategoryNavigation, {})}  ${maybeRenderHead()}<section class="py-16 bg-white"> <div class="container"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Featured Collections</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Discover our most popular digital images and artwork, carefully curated for quality and creativity
</p> </div> ${error ? renderTemplate`<div class="text-center py-16"> <div class="inline-flex items-center gap-3 text-warning-600 bg-warning-50 px-6 py-4 rounded-xl border border-warning-200"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path> </svg> <span class="font-medium">${error}</span> </div> </div>` : featuredProducts.length === 0 ? renderTemplate`<div class="text-center py-16"> <div class="inline-flex items-center gap-3 text-primary-600"> <svg class="animate-spin w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path> </svg> <span>Loading featured products...</span> </div> </div>` : renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${featuredProducts.map((product) => renderTemplate`${renderComponent($$result2, "ProductCard", $$ProductCard, { "product": product })}`)} </div>`} ${featuredProducts.length > 0 && renderTemplate`<div class="text-center mt-12"> <a href="/products" class="btn-primary text-lg px-8 py-4">
View All Products
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path> </svg> </a> </div>`} </div> </section>  <section class="py-16 bg-primary-50"> <div class="container"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Why Choose Our Images?</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Professional quality, instant access, and commercial licensing for all your creative projects
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Premium Quality</h3> <p class="text-primary-600 leading-relaxed">Professional-grade digital images in ultra-high resolution for stunning results</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Instant Access</h3> <p class="text-primary-600 leading-relaxed">Download your images immediately after purchase with secure, lifetime access</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-warning-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Commercial License</h3> <p class="text-primary-600 leading-relaxed">Full commercial rights included - use for personal and business projects without limits</p> </div> </div> </div> </section> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/index.astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
