---
export interface Props {
  initialSearch?: string;
  initialPriceRange?: [number, number];
  initialTags?: string[];
}

const { 
  initialSearch = '', 
  initialPriceRange = [0, 100],
  initialTags = []
} = Astro.props;
---

<div class="bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-100">
  <div class="mb-6">
    <div class="flex gap-3">
      <input
        type="text"
        id="searchInput"
        placeholder="Search products..."
        value={initialSearch}
        class="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors"
      />
      <button
        class="px-6 py-3 bg-primary-600 text-white rounded-full hover:bg-primary-700 transition-colors font-medium"
        onclick="performSearch()"
      >
        🔍
      </button>
    </div>
  </div>

  <div class="flex items-center justify-between">
    <button
      id="filterToggleBtn"
      class="flex items-center gap-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors"
    >
      <span>Filters</span>
      <span>⚙️</span>
    </button>

    <div class="hidden mt-6 p-6 bg-gray-50 rounded-full border border-gray-200" id="filterPanel">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="space-y-3">
          <label class="block text-sm font-medium text-gray-700">Price Range</label>
          <div class="space-y-3">
            <input
              type="range"
              id="minPrice"
              min="0"
              max="100"
              value={initialPriceRange[0]}
              class="w-full h-2 bg-gray-200 rounded-full appearance-none cursor-pointer slider"
              oninput="updatePriceRange()"
            />
            <input
              type="range"
              id="maxPrice"
              min="0"
              max="100"
              value={initialPriceRange[1]}
              class="w-full h-2 bg-gray-200 rounded-xl appearance-none cursor-pointer slider"
              oninput="updatePriceRange()"
            />
            <div class="text-center">
              <span id="priceDisplay" class="text-sm font-medium text-gray-600">$0 - $100</span>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <label class="block text-sm font-medium text-gray-700">Sort By</label>
          <select
            id="sortBy"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors bg-white"
            onchange="applySorting()"
          >
            <option value="name">Name (A-Z)</option>
            <option value="name-desc">Name (Z-A)</option>
            <option value="price">Price (Low to High)</option>
            <option value="price-desc">Price (High to Low)</option>
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
          </select>
        </div>

        <div class="space-y-3">
          <label class="block text-sm font-medium text-gray-700">Tags</label>
          <div class="flex flex-wrap gap-2" id="tagsFilter">
            <!-- Tags will be populated dynamically -->
          </div>
        </div>

        <div class="space-y-3">
          <label class="block text-sm font-medium text-gray-700">Actions</label>
          <div class="flex gap-2">
            <button
              class="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors text-sm font-medium"
              onclick="clearAllFilters()"
            >
              Clear All
            </button>
            <button
              class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 transition-colors text-sm font-medium"
              onclick="applyFilters()"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-4">
    <span id="resultsCount" class="text-sm text-gray-600">Loading products...</span>
  </div>
</div>

<style>
  /* Custom slider styles for better cross-browser compatibility */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
</style>

<script>
  // Global variables for filtering
  let allProducts: any[] = [];
  let filteredProducts: any[] = [];
  let availableTags = new Set<string>();

  // Initialize search and filter functionality
  document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    loadProducts();

    // Add event listener for filter toggle
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    if (filterToggleBtn) {
      filterToggleBtn.addEventListener('click', toggleFilters);
    }
  });

  // Toggle filter panel
  function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
      filterPanel.classList.toggle('hidden');
    }
  }

  async function loadProducts() {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      allProducts = data.products || [];
      
      // Extract all unique tags
      allProducts.forEach(product => {
        if (product.tags) {
          product.tags.forEach(tag => availableTags.add(tag));
        }
      });
      
      populateTagsFilter();
      filteredProducts = [...allProducts];
      updateResultsCount();
      renderProducts();
    } catch (error) {
      console.error('Error loading products:', error);
      document.getElementById('resultsCount').textContent = 'Error loading products';
    }
  }

  function populateTagsFilter() {
    const tagsContainer = document.getElementById('tagsFilter');
    if (!tagsContainer) return;
    
    tagsContainer.innerHTML = '';
    
    Array.from(availableTags).sort().forEach(tag => {
      const tagElement = document.createElement('div');
      tagElement.className = 'tag-checkbox';
      tagElement.innerHTML = `
        <input type="checkbox" id="tag-${tag}" value="${tag}" onchange="updateTagFilter()">
        <label for="tag-${tag}">#${tag}</label>
      `;
      tagsContainer.appendChild(tagElement);
    });
  }

  function initializeFilters() {
    updatePriceRange();
  }

  function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    if (panel) {
      panel.classList.toggle('active');
    }
  }

  function updatePriceRange() {
    const minPrice = document.getElementById('minPrice');
    const maxPrice = document.getElementById('maxPrice');
    const display = document.getElementById('priceDisplay');
    
    if (minPrice && maxPrice && display) {
      const min = parseInt(minPrice.value);
      const max = parseInt(maxPrice.value);
      
      // Ensure min <= max
      if (min > max) {
        minPrice.value = max;
      }
      
      display.textContent = `$${minPrice.value} - $${maxPrice.value}`;
    }
  }

  function performSearch() {
    applyFilters();
  }

  function applySorting() {
    const sortBy = document.getElementById('sortBy').value;
    
    filteredProducts.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'price':
          return a.price - b.price;
        case 'price-desc':
          return b.price - a.price;
        case 'newest':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'oldest':
          return new Date(a.createdAt) - new Date(b.createdAt);
        default:
          return 0;
      }
    });
    
    renderProducts();
  }

  function updateTagFilter() {
    // This will be called when tag checkboxes change
    // The actual filtering will happen in applyFilters()
  }

  function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const minPrice = parseInt(document.getElementById('minPrice').value);
    const maxPrice = parseInt(document.getElementById('maxPrice').value);
    
    // Get selected tags
    const selectedTags = Array.from(document.querySelectorAll('#tagsFilter input[type="checkbox"]:checked'))
      .map(checkbox => checkbox.value);
    
    filteredProducts = allProducts.filter(product => {
      // Search filter
      const matchesSearch = !searchTerm || 
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm);
      
      // Price filter
      const matchesPrice = product.price >= minPrice && product.price <= maxPrice;
      
      // Tags filter
      const matchesTags = selectedTags.length === 0 || 
        (product.tags && selectedTags.some(tag => product.tags.includes(tag)));
      
      return matchesSearch && matchesPrice && matchesTags;
    });
    
    applySorting();
    updateResultsCount();
  }

  function clearAllFilters() {
    // Reset search
    document.getElementById('searchInput').value = '';
    
    // Reset price range
    document.getElementById('minPrice').value = 0;
    document.getElementById('maxPrice').value = 100;
    updatePriceRange();
    
    // Reset sort
    document.getElementById('sortBy').value = 'name';
    
    // Reset tags
    document.querySelectorAll('#tagsFilter input[type="checkbox"]').forEach(checkbox => {
      checkbox.checked = false;
    });
    
    // Apply filters
    applyFilters();
  }

  function updateResultsCount() {
    const count = filteredProducts.length;
    const total = allProducts.length;
    const resultsElement = document.getElementById('resultsCount');
    
    if (resultsElement) {
      resultsElement.textContent = `Showing ${count} of ${total} products`;
    }
  }

  function renderProducts() {
    // This function will trigger a custom event that the products page can listen to
    const event = new CustomEvent('productsFiltered', {
      detail: { products: filteredProducts }
    });
    document.dispatchEvent(event);
  }

  // Handle Enter key in search input
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.id === 'searchInput') {
      performSearch();
    }
  });
</script>
