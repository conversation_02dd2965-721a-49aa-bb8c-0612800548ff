globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createComponent, a as createAstro, m as maybeRenderHead, b as addAttribute, f as renderScript, r as renderTemplate, g as renderComponent } from '../../chunks/astro/server_DkHHQTPZ.mjs';
import { $ as $$Layout } from '../../chunks/Layout_R4AeOjRm.mjs';
/* empty css                                     */
import { c as createPolarClient, t as transformPolarProduct, f as formatPrice } from '../../chunks/polar_mR5Jg937.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro$1 = createAstro();
const $$ImageGallery = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$ImageGallery;
  const { images, productName } = Astro2.props;
  const displayImages = images.length > 0 ? images : ["/placeholder-image.svg"];
  return renderTemplate`${maybeRenderHead()}<div class="flex flex-col gap-4" data-astro-cid-gjhjmbi3> <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group" data-astro-cid-gjhjmbi3> <img id="mainImage"${addAttribute(displayImages[0], "src")}${addAttribute(productName, "alt")} onclick="openLightbox(0)" class="max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105" data-astro-cid-gjhjmbi3> <button class="absolute top-4 right-4 bg-black/70 text-white border-none rounded-full w-10 h-10 text-lg cursor-pointer transition-all duration-200 opacity-0 group-hover:opacity-100 hover:bg-black/90 hover:scale-110 flex items-center justify-center" onclick="openLightbox(0)" data-astro-cid-gjhjmbi3>
🔍
</button> </div> ${displayImages.length > 1 && renderTemplate`<div class="grid grid-cols-[repeat(auto-fit,minmax(80px,1fr))] gap-2 max-h-24 overflow-x-auto" data-astro-cid-gjhjmbi3> ${displayImages.map((image, index) => renderTemplate`<div${addAttribute(`aspect-square rounded-full overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:border-primary-500 hover:scale-105 ${index === 0 ? "border-primary-500" : "border-transparent"}`, "class")} data-astro-cid-gjhjmbi3> <img${addAttribute(image, "src")}${addAttribute(`${productName} - Image ${index + 1}`, "alt")}${addAttribute(`changeMainImage('${image}', ${index})`, "onclick")} loading="lazy" class="w-full h-full object-cover" data-astro-cid-gjhjmbi3> </div>`)} </div>`} </div> <!-- Lightbox Modal --> <div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in" data-astro-cid-gjhjmbi3> <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto" data-astro-cid-gjhjmbi3> <button class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors" onclick="closeLightbox()" data-astro-cid-gjhjmbi3>
&times;
</button> <button class="absolute top-1/2 -translate-y-1/2 -left-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="prevImage()" data-astro-cid-gjhjmbi3>
&#8249;
</button> <img id="lightboxImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg" data-astro-cid-gjhjmbi3> <button class="absolute top-1/2 -translate-y-1/2 -right-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="nextImage()" data-astro-cid-gjhjmbi3>
&#8250;
</button> <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full" data-astro-cid-gjhjmbi3> <span id="imageCounter" data-astro-cid-gjhjmbi3>1 / 1</span> </div> </div> </div>  ${renderScript($$result, "D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/code/image/polar-image-store/src/components/ImageGallery.astro", void 0);

const $$Astro = createAstro();
async function getStaticPaths() {
  try {
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ;
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    const productList = response.result?.items || [];
    const products = productList.map(transformPolarProduct);
    return products.map((product) => ({
      params: { slug: product.slug },
      props: { product }
    }));
  } catch (error) {
    console.error("Error generating static paths:", error);
    return [];
  }
}
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { product } = Astro2.props;
  let checkoutUrl = "";
  try {
    const polar = createPolarClient();
    const checkoutLink = await polar.checkoutLinks.create({
      paymentProcessor: "stripe",
      productId: product.id,
      allowDiscountCodes: true,
      requireBillingAddress: false,
      successUrl: `${"http://localhost:4321"}/success`
    });
    checkoutUrl = checkoutLink.url;
  } catch (error) {
    console.error("Error creating checkout URL:", error);
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${product.name} - Polar Image Store`, "description": product.description }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-7xl mx-auto"> <div class="mb-8"> <nav class="flex items-center gap-2 text-sm text-primary-600"> <a href="/" class="hover:text-accent-600 transition-colors">Home</a> <span>/</span> <a href="/products" class="hover:text-accent-600 transition-colors">Products</a> <span>/</span> <span class="text-primary-900 font-medium">${product.name}</span> </nav> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <div class="lg:sticky lg:top-8 lg:self-start"> ${renderComponent($$result2, "ImageGallery", $$ImageGallery, { "images": product.images, "productName": product.name })} </div> <div class="space-y-8"> <div> <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">${product.name}</h1> </div> <div class="prose prose-gray max-w-none"> <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3> <p class="text-primary-700 leading-relaxed">${product.description}</p> </div> ${product.tags && product.tags.length > 0 && renderTemplate`<div> <h3 class="text-lg font-semibold text-gray-900 mb-3">Tags</h3> <div class="flex flex-wrap gap-2"> ${product.tags.map((tag) => renderTemplate`<span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
#${tag} </span>`)} </div> </div>`} <div class="pt-6 border-t border-primary-200"> <div class="flex gap-3"> ${checkoutUrl ? renderTemplate`<a${addAttribute(checkoutUrl, "href")} class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path> </svg> <span>Buy Now - ${formatPrice(product.price, product.currency)}</span> </a>` : renderTemplate`<button class="flex-1 bg-primary-300 text-primary-600 px-6 py-3 rounded-full font-semibold text-base cursor-not-allowed" disabled>
Checkout Unavailable
</button>`} <button class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path> </svg> <span>Share</span> </button> </div> </div> <div class="bg-primary-50 rounded-2xl p-6"> <h3 class="text-lg font-semibold text-primary-900 mb-4">Product Details</h3> <ul class="space-y-3 text-primary-700"> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Format:</strong> High-resolution digital image</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>License:</strong> Commercial use allowed</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Delivery:</strong> Instant download after purchase</span> </li> <li class="flex items-start gap-3"> <svg class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> <span><strong>Support:</strong> Email support included</span> </li> </ul> </div> </div> </div> <div class="mt-16 text-center"> <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2> <p class="text-primary-600 mb-8">Browse our full collection of digital images</p> <a href="/products" class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5">
View All Products
<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path> </svg> </a> </div> </div> ` })}`;
}, "D:/code/image/polar-image-store/src/pages/products/[slug].astro", void 0);
const $$file = "D:/code/image/polar-image-store/src/pages/products/[slug].astro";
const $$url = "/products/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
