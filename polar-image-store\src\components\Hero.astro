---
export interface Props {
  title: string;
  subtitle: string;
}

const {
  title,
  subtitle
} = Astro.props;
---

<section class="relative py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
  <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl"></div>
  
  <div class="container relative">
    <div class="max-w-4xl mx-auto">
      <!-- Main title -->
      <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-4 leading-tight max-w-3xl text-left" set:html={title}>
      </h1>

      <!-- Subtitle -->
      <p class="text-base md:text-lg lg:text-xl text-primary-600 max-w-2xl leading-relaxed">
        {subtitle}
      </p>
    </div>
  </div>
</section>

<style>
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
</style>
