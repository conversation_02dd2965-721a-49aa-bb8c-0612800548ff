globalThis.process ??= {}; globalThis.process.env ??= {};
import { C as Checkout } from '../../chunks/index_C4LTQoxk.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const GET = Checkout({
  accessToken: "polar_oat_ouH54pn5flleg2O77vuEz0JdL0plgG6L9u74d4QLM5Z",
  successUrl: `${"http://localhost:4321"}/success`,
  server: "production"
  // Always use production
});

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
